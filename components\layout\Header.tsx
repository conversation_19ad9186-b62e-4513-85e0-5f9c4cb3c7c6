"use client";

import {
  MoonIcon,
  SunIcon,
  Menu,
  MessageCircle,
  LogOut,
  Settings,
  User,
  KeyIcon,
  Home,
  FileText,
  File,
  CheckSquare,
  Users,
  BarChart,
  Server,
  Shield,
  ChevronDown,
  Calendar,
} from "lucide-react";
import { useTheme } from "next-themes";
import { Button } from "../ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "../ui/dropdown-menu";
import { Badge } from "../ui/badge";
import { Sheet, SheetContent, SheetTrigger } from "../ui/sheet";
import { useState, useCallback, memo, useMemo } from "react";
import { useRouter } from "next/navigation";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";

import { useToast } from "@/hooks/useToast";
import { useAuth } from "@/hooks/useAuth";
import { NotificationsPanel } from "@/components/notifications/NotificationsPanel";
import { getProfileImageUrl } from "@/lib/api/storage";
import { useNotifications } from "@/hooks/useNotifications";

// Define role-specific navigation items
const ROLE_NAVIGATION_ITEMS = {
  student: [
    { name: "Dashboard", href: "/dashboard", showNotification: true, icon: "Home" },
    { name: "My Projects", href: "/projects", showNotification: false, icon: "FileText" },
    { name: "Calendar", href: "/calendar", showNotification: false, icon: "Calendar" },
    { name: "Chat", href: "/chat", showNotification: true, icon: "MessageCircle" },
    { name: "Documents", href: "/documents", showNotification: false, icon: "File" },
  ],
  supervisor: [
    { name: "Dashboard", href: "/dashboard", showNotification: true, icon: "Home" },
    { name: "Projects", href: "/projects", showNotification: false, icon: "FileText" },
    { name: "Calendar", href: "/calendar", showNotification: false, icon: "Calendar" },
    { name: "Chat", href: "/chat", showNotification: true, icon: "MessageCircle" },
    // { name: "Documents", href: "/documents", showNotification: false, icon: "File" },
    { name: "Reviews", href: "/reviews", showNotification: true, icon: "CheckSquare" },
  ],
  manager: [
    { name: "Dashboard", href: "/dashboard", showNotification: true, icon: "Home" },
    { name: "Projects", href: "/projects", showNotification: false, icon: "FileText" },
    { name: "Calendar", href: "/calendar", showNotification: false, icon: "Calendar" },
    { name: "Users", href: "/users", showNotification: false, icon: "Users" },
    { name: "Chat", href: "/chat", showNotification: true, icon: "MessageCircle" },
    // { name: "Documents", href: "/documents", showNotification: false, icon: "File" },
    { name: "Reports", href: "/reports", showNotification: false, icon: "BarChart" },
  ],
  admin: [
    { name: "Dashboard", href: "/dashboard", showNotification: true, icon: "Home" },
    { name: "Projects", href: "/projects", showNotification: false, icon: "FileText" },
    { name: "Calendar", href: "/calendar", showNotification: false, icon: "Calendar" },
    { name: "Users", href: "/users", showNotification: false, icon: "Users" },
    { name: "Chat", href: "/chat", showNotification: true, icon: "MessageCircle" },
    // { name: "Documents", href: "/documents", showNotification: false, icon: "File" },
    { name: "Settings", href: "/admin/settings", showNotification: false, icon: "Settings" },
    { name: "System", href: "/admin/system", showNotification: false, icon: "Server" },
  ],
} as const;

// Function to get navigation items based on user role
const getNavigationItems = (user: { role?: string } | null | undefined) => {
  if (!user || !user.role) return ROLE_NAVIGATION_ITEMS.student;

  const role = user.role as keyof typeof ROLE_NAVIGATION_ITEMS;
  return ROLE_NAVIGATION_ITEMS[role] || ROLE_NAVIGATION_ITEMS.student;
};

interface NavigationItemProps {
  item: {
    name: string;
    href: string;
    showNotification?: boolean;
    icon?: string;
  };
  unreadCount: number;
  onClick: (href: string) => void;
}

// Helper function to get the icon component based on icon name
const getIconComponent = (iconName?: string) => {
  if (!iconName) return null;

  const iconMap: Record<string, React.ReactNode> = {
    Home: <Home className="h-4 w-4 mr-2" />,
    FileText: <FileText className="h-4 w-4 mr-2" />,
    File: <File className="h-4 w-4 mr-2" />,
    Calendar: <Calendar className="h-4 w-4 mr-2" />,
    MessageCircle: <MessageCircle className="h-4 w-4 mr-2" />,
    CheckSquare: <CheckSquare className="h-4 w-4 mr-2" />,
    Users: <Users className="h-4 w-4 mr-2" />,
    BarChart: <BarChart className="h-4 w-4 mr-2" />,
    Settings: <Settings className="h-4 w-4 mr-2" />,
    Server: <Server className="h-4 w-4 mr-2" />,
    Shield: <Shield className="h-4 w-4 mr-2" />,
  };

  return iconMap[iconName] || null;
};

// Memoized navigation item to prevent unnecessary re-renders
const NavigationItem = memo(
  ({ item, unreadCount, onClick }: NavigationItemProps) => (
    <Button
      variant="ghost"
      className="relative flex items-center"
      onClick={() => onClick(item.href)}
    >
      {item.icon && getIconComponent(item.icon)}
      <span>{item.name}</span>
      {item.showNotification && unreadCount > 0 && (
        <span className="absolute -top-1 -right-1 bg-primary text-primary-foreground text-xs rounded-full px-2 py-0.5 flex items-center gap-1">
          <MessageCircle className="h-3 w-3" />
          {unreadCount}
        </span>
      )}
    </Button>
  )
);
NavigationItem.displayName = "NavigationItem";

// Memoized mobile navigation item
const MobileNavItem = memo(
  ({ item, unreadCount, onClick }: NavigationItemProps) => (
    <button
      className="flex items-center justify-between w-full px-2 py-2 text-lg font-medium text-left hover:text-primary transition-colors rounded-md hover:bg-accent"
      onClick={() => onClick(item.href)}
    >
      <div className="flex items-center">
        {item.icon && getIconComponent(item.icon)}
        <span>{item.name}</span>
      </div>
      {item.showNotification && unreadCount > 0 && (
        <span className="bg-primary text-primary-foreground text-xs rounded-full px-2 py-1 flex items-center gap-1">
          <MessageCircle className="h-3 w-3" />
          {unreadCount}
        </span>
      )}
    </button>
  )
);
MobileNavItem.displayName = "MobileNavItem";

export function Header() {
  const { setTheme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();
  const { user, logout } = useAuth();
  const { toast } = useToast();

  // Get notifications
  const {
    notifications,
    isLoading: isLoadingNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    deleteAllNotifications,
    unreadCount
  } = useNotifications(user?.id || null);

  // Get profile image with fallback
  const profileImage = useMemo(
    () => (user?.id ? getProfileImageUrl(user.id) : undefined),
    [user?.id]
  );

  // Memoize handlers to prevent unnecessary re-renders
  const handleNavigation = useCallback(
    (href: string) => {
      setIsOpen(false);
      router.push(href);
    },
    [router]
  );

  const handleLogout = useCallback(async () => {
    try {
      await logout();
      router.push("/auth/login");
    } catch (error) {
      console.error("Error logging out:", error);
      toast({
        variant: "destructive",
        title: "Logout failed",
        description:
          error instanceof Error ? error.message : "Please try again",
      });
    }
  }, [logout, toast, router]);

  // Theme setters
  const themeHandlers = useMemo(
    () => ({
      light: () => setTheme("light"),
      dark: () => setTheme("dark"),
      system: () => setTheme("system"),
    }),
    [setTheme]
  );

  // Navigation handlers
  const navigationHandlers = useMemo(
    () => ({
      dashboard: () => handleNavigation("/dashboard"),
      profile: () => handleNavigation("/profile"),
      settings: () => handleNavigation("/settings"),
      changePassword: () => handleNavigation("/auth/change-password"),
    }),
    [handleNavigation]
  );

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-14 items-center justify-between gap-4">
          {/* Left section - Logo and mobile menu */}
          <div className="flex items-center gap-2">
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="md:hidden">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-[240px] sm:w-[300px]">
                <nav className="flex flex-col gap-4 mt-8">
                  {getNavigationItems(user).map((item) => (
                    <MobileNavItem
                      key={item.name}
                      item={item}
                      unreadCount={item.showNotification ? unreadCount : 0}
                      onClick={handleNavigation}
                    />
                  ))}
                  <DropdownMenuSeparator className="my-2" />
                  <button
                    className="flex items-center w-full px-2 py-2 text-lg font-medium text-left text-destructive hover:text-destructive/80 transition-colors rounded-md hover:bg-accent"
                    onClick={handleLogout}
                  >
                    <LogOut className="mr-2 h-5 w-5" />
                    Logout
                  </button>
                </nav>
              </SheetContent>
            </Sheet>
            <Button
              variant="ghost"
              className="font-bold hidden sm:flex"
              onClick={navigationHandlers.dashboard}
            >
              Thesis Management
            </Button>
            <Button
              variant="ghost"
              className="font-bold sm:hidden"
              onClick={navigationHandlers.dashboard}
            >
              TM
            </Button>
          </div>

          {/* Center section - Navigation */}
          <nav className="hidden md:flex items-center justify-center flex-1">
            <div className="flex items-center gap-1 sm:gap-2 lg:gap-6">
              {getNavigationItems(user).map((item) => (
                <NavigationItem
                  key={item.name}
                  item={item}
                  unreadCount={item.showNotification ? unreadCount : 0}
                  onClick={handleNavigation}
                />
              ))}
            </div>
          </nav>

          {/* Right section - Theme toggle, notifications and profile dropdown */}
          <div className="flex items-center gap-2">
            <NotificationsPanel
              notifications={notifications}
              isLoading={isLoadingNotifications}
              onMarkAsRead={markAsRead}
              onMarkAllAsRead={markAllAsRead}
              onDelete={deleteNotification}
              onDeleteAll={deleteAllNotifications}
            />

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-9 w-9 relative">
                  <SunIcon className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
                  <MoonIcon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
                  <span className="sr-only">Toggle theme</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={themeHandlers.light}>
                  Light
                </DropdownMenuItem>
                <DropdownMenuItem onClick={themeHandlers.dark}>
                  Dark
                </DropdownMenuItem>
                <DropdownMenuItem onClick={themeHandlers.system}>
                  System
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Profile dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-9 w-9 rounded-full overflow-hidden p-0"
                >
                  <Avatar>
                    <AvatarImage
                      src={profileImage || user?.profileImage}
                      alt={user?.name || "User"}
                    />
                    <AvatarFallback>
                      {user?.name?.charAt(0) || "U"}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel className="flex flex-col gap-1">
                  <div className="font-medium">{user?.name}</div>
                  {user?.role && (
                    <Badge
                      variant="outline"
                      className={`capitalize w-fit ${
                        user.role === "admin"
                          ? "border-red-200 bg-red-50 text-red-700 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800"
                          : user.role === "manager"
                          ? "border-amber-200 bg-amber-50 text-amber-700 dark:bg-amber-900/20 dark:text-amber-300 dark:border-amber-800"
                          : user.role === "supervisor"
                          ? "border-blue-200 bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                          : "border-green-200 bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800"
                      }`}
                    >
                      {user.role}
                    </Badge>
                  )}
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={navigationHandlers.profile}>
                  <User className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={navigationHandlers.settings}>
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={navigationHandlers.changePassword}>
                  <KeyIcon className="mr-2 h-4 w-4" />
                  <span>Change Password</span>
                </DropdownMenuItem>

                {/* Role-specific actions */}
                {user?.role === 'admin' && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => handleNavigation('/admin/system')}>
                      <Server className="mr-2 h-4 w-4" />
                      <span>System Settings</span>
                    </DropdownMenuItem>
                  </>
                )}

                {user?.role === 'manager' && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => handleNavigation('/reports')}>
                      <BarChart className="mr-2 h-4 w-4" />
                      <span>Reports</span>
                    </DropdownMenuItem>
                  </>
                )}

                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  );
}



