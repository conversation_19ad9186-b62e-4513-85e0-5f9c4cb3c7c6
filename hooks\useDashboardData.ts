import { useQueryClient, UseQueryOptions, QueryKey } from "@tanstack/react-query";
import { useProject } from "@/hooks/useProject";
import { useUser } from "@/hooks/useUser";
import { useMemo, useCallback } from "react";
import { User, Project, ProjectStatus, UserRole } from "@/lib/types";
import { DashboardData } from "@/lib/types/dashboard";
import { getErrorMessage } from "@/lib/utils/error-handling";

/**
 * Extended DashboardData interface to include the raw query objects and additional features
 * that aren't in the original DashboardData interface
 */
interface ExtendedDashboardData extends DashboardData {
  // Raw query objects
  usersQuery: ReturnType<ReturnType<typeof useUser>["useUsersQuery"]>;
  projectsQuery: ReturnType<ReturnType<typeof useProject>["useProjectsQuery"]>;

  // Additional status indicators
  isFetching: boolean;
  isRefetching: boolean;

  // Additional data
  usersByRole: Record<UserRole, User[]>;
  projectsByStatus: Record<ProjectStatus, Project[]>;

  // Enhanced actions
  prefetch: () => Promise<unknown[]>;
  invalidateData: () => Promise<void>;

  // Query keys for external use
  queryKeys: {
    users: QueryKey;
    projects: QueryKey;
    milestones: QueryKey;
    documents: QueryKey;
  };
}

/**
 * Filter options for dashboard data
 */
export interface DashboardFilterOptions {
  projectStatus?: ProjectStatus | 'all';
  userRole?: UserRole | 'all';
  searchTerm?: string;
  sortBy?: 'createdAt' | 'title' | 'lastActivity';
  sortDirection?: 'asc' | 'desc';
}

/**
 * Configuration options for the useDashboardData hook
 */
interface UseDashboardDataOptions {
  /** Enable/disable users query */
  enableUsersQuery?: boolean;
  /** Enable/disable projects query */
  enableProjectsQuery?: boolean;
  /** Custom options for users query */
  usersQueryOptions?: Omit<UseQueryOptions<User[], unknown>, 'queryKey' | 'queryFn'>;
  /** Custom options for projects query */
  projectsQueryOptions?: Omit<UseQueryOptions<Project[], unknown>, 'queryKey' | 'queryFn'>;
  /** Filter options for the returned data */
  filters?: DashboardFilterOptions;
}

/**
 * Custom hook for fetching and managing dashboard data
 *
 * This hook centralizes data fetching for dashboard components by leveraging
 * existing query hooks for users and projects. It provides:
 * - Access to raw query objects for advanced use cases
 * - Processed data with proper null handling
 * - Derived data like supervisors list and categorized data
 * - Unified loading and error states
 * - Comprehensive data refresh and invalidation functions
 * - Filtering and sorting capabilities
 * - Performance optimizations with proper caching
 *
 * @param options Configuration options for the hook
 * @returns Enhanced dashboard data including users, projects, loading state, and utility functions
 */
export function useDashboardData(options: UseDashboardDataOptions = {}): ExtendedDashboardData {
  const {
    projectsQueryOptions = {},
    enableProjectsQuery = true,
    filters = {}
  } = options;

  const queryClient = useQueryClient();
  const { useProjectsQuery, getProjectsQueryKey } = useProject();
  const { useUsersQuery } = useUser();

  // Define query keys for better organization
  const usersKey = ["users"];
  const projectsKey = getProjectsQueryKey();
  const milestonesKey = ["milestones"];
  const documentsKey = ["documents"];

  // Leverage existing hooks for data fetching with proper options
  const usersQuery = useUsersQuery();

  const projectsQuery = useProjectsQuery(undefined, projectsQueryOptions);

  // Apply filters and sorting to the data
  const {
    filteredUsers,
    filteredProjects,
    usersByRole,
    projectsByStatus
  } = useMemo(() => {
    // Initialize with empty arrays to handle loading state
    let users = usersQuery.data || [];
    let projects = projectsQuery.data || [];

    // Apply search filter if provided
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();

      users = users.filter(user =>
        user.name.toLowerCase().includes(searchLower) ||
        user.email.toLowerCase().includes(searchLower) ||
        (user.department && user.department.toLowerCase().includes(searchLower))
      );

      projects = projects.filter(project =>
        project.title.toLowerCase().includes(searchLower) ||
        (project.description && project.description.toLowerCase().includes(searchLower))
      );
    }

    // Apply role filter to users
    if (filters.userRole && filters.userRole !== 'all') {
      users = users.filter(user => user.role === filters.userRole);
    }

    // Apply status filter to projects
    if (filters.projectStatus && filters.projectStatus !== 'all') {
      projects = projects.filter(project => project.status === filters.projectStatus);
    }

    // Apply sorting to projects
    if (filters.sortBy) {
      const direction = filters.sortDirection === 'desc' ? -1 : 1;

      projects = [...projects].sort((a, b) => {
        if (filters.sortBy === 'title') {
          return direction * a.title.localeCompare(b.title);
        } else if (filters.sortBy === 'lastActivity') {
          const dateA = a.lastActivity ? new Date(a.lastActivity).getTime() : 0;
          const dateB = b.lastActivity ? new Date(b.lastActivity).getTime() : 0;
          return direction * (dateB - dateA);
        } else if (filters.sortBy === 'createdAt') {
          const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
          const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
          return direction * (dateB - dateA);
        }
        return 0;
      });
    }

    // Group users by role
    const usersByRole = users.reduce((acc, user) => {
      const role = user.role as UserRole;
      if (!acc[role]) {
        acc[role] = [];
      }
      acc[role].push(user);
      return acc;
    }, {} as Record<UserRole, User[]>);

    // Group projects by status
    const projectsByStatus = projects.reduce((acc, project) => {
      const status = project.status as ProjectStatus;
      if (!acc[status]) {
        acc[status] = [];
      }
      acc[status].push(project);
      return acc;
    }, {} as Record<ProjectStatus, Project[]>);

    return {
      filteredUsers: users,
      filteredProjects: projects,
      usersByRole,
      projectsByStatus
    };
  }, [
    usersQuery.data,
    projectsQuery.data,
    filters.searchTerm,
    filters.userRole,
    filters.projectStatus,
    filters.sortBy,
    filters.sortDirection
  ]);

  // Extract supervisors from users with proper type safety
  const supervisors = useMemo(() => {
    return usersByRole.supervisor || [];
  }, [usersByRole]);

  // Enhanced comprehensive data refresh function with error handling and status tracking
  const refreshDashboardData = useCallback(async () => {
    // Track if we're currently refreshing to prevent duplicate calls
    if (usersQuery.isRefetching || projectsQuery.isRefetching) {
      console.log('Dashboard refresh already in progress, skipping duplicate request');
      return null;
    }

    try {
      // Return a promise that resolves when both main queries are refetched
      const results = await Promise.all([
        usersQuery.refetch(),
        projectsQuery.refetch()
      ]);

      // Log success in development mode
      if (process.env.NODE_ENV === 'development') {
        console.log('Dashboard data refreshed successfully');
      }

      return results;
    } catch (error) {
      // Log the error for debugging
      console.error('Error refreshing dashboard data:', error);

      // Re-throw to allow calling code to handle the error
      throw error;
    }
  }, [usersQuery, projectsQuery]);

  // Invalidate all dashboard data
  const invalidateDashboardData = useCallback(async () => {
    await Promise.all([
      queryClient.invalidateQueries({ queryKey: usersKey }),
      queryClient.invalidateQueries({ queryKey: projectsKey }),
      queryClient.invalidateQueries({ queryKey: milestonesKey }),
      queryClient.invalidateQueries({ queryKey: documentsKey })
    ]);
  }, [queryClient, usersKey, projectsKey, milestonesKey, documentsKey]);

  // Prefetch function for improving UX
  const prefetchDashboardData = useCallback(() => {
    return Promise.all([
      queryClient.prefetchQuery({
        queryKey: usersKey,
        queryFn: async () => {
          const { data } = await usersQuery.refetch();
          return data;
        }
      }),
      queryClient.prefetchQuery({
        queryKey: projectsKey,
        queryFn: async () => {
          const { data } = await projectsQuery.refetch();
          return data;
        }
      })
    ]);
  }, [queryClient, usersKey, projectsKey, usersQuery, projectsQuery]);

  // Enhanced loading and error states
  const loading = usersQuery.isLoading || projectsQuery.isLoading;
  const isFetching = usersQuery.isFetching || projectsQuery.isFetching;
  const isRefetching = usersQuery.isRefetching || projectsQuery.isRefetching;

  // Improved error handling with detailed error information
  const error = useMemo(() => {
    if (usersQuery.error || projectsQuery.error) {
      const errorSource = usersQuery.error ? 'Users' : 'Projects';
      const errorObj = usersQuery.error || projectsQuery.error;
      const errorMessage = getErrorMessage(errorObj);

      // Log detailed error information in development
      if (process.env.NODE_ENV === 'development') {
        console.error(`Dashboard data error (${errorSource}):`, errorObj);
      }

      return errorMessage;
    }
    return null;
  }, [usersQuery.error, projectsQuery.error]);

  return {
    // Raw query objects for advanced use cases
    usersQuery,
    projectsQuery,

    // Processed data with proper null handling
    users: filteredUsers,
    projects: filteredProjects,
    supervisors,

    // Categorized data
    usersByRole,
    projectsByStatus,

    // Status indicators
    loading,
    isFetching,
    isRefetching,
    error,

    // Actions
    refetch: refreshDashboardData,
    prefetch: prefetchDashboardData,
    invalidateData: invalidateDashboardData,

    // Query keys for external use
    queryKeys: {
      users: usersKey,
      projects: projectsKey,
      milestones: milestonesKey,
      documents: documentsKey
    }
  };
}




