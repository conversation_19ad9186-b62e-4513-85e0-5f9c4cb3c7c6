"use client";

import { useState, useMemo, useCallback } from "react";
import { Head<PERSON> } from "@/components/layout/Header";
import { useAuth } from "@/hooks/useAuth";
import { useDashboardData } from "@/hooks/useDashboardData";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend,
} from "recharts";
import {
  Download,
  FileText,
  Users,
  Calendar,
  Bar<PERSON>hart as BarChartIcon,
  AlertCircle,
} from "lucide-react";
import { generateAnalyticsData } from "@/lib/utils/analytics";
import { format, subMonths } from "date-fns";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Project } from "@/lib/types";

// Define colors for status indicators with semantic naming
const STATUS_COLORS = {
  active: "#10b981", // green
  completed: "#3b82f6", // blue
  archived: "#6b7280", // gray
  suspended: "#ef4444", // red
  pending_supervisor: "#f59e0b", // amber
};

// Define types for time range and tab values
type TimeRange = "month" | "quarter" | "year";
type TabValue = "overview" | "projects" | "users" | "timeline";

// Custom tooltip component for charts
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background border rounded-md shadow-md p-3">
        <p className="font-medium">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={`item-${index}`} style={{ color: entry.color }}>
            {entry.name}: {entry.value}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

// Stat card component for metrics
interface StatCardProps {
  title: string;
  value: number | string;
  description?: string;
  trend?: number;
  loading?: boolean;
}

const StatCard = ({ title, value, description, trend, loading }: StatCardProps) => (
  <div className="flex flex-col gap-1 p-4 border rounded-lg bg-card">
    {loading ? (
      <>
        <Skeleton className="h-4 w-24 mb-2" />
        <Skeleton className="h-8 w-16" />
        {description && <Skeleton className="h-4 w-32 mt-1" />}
      </>
    ) : (
      <>
        <span className="text-sm font-medium text-muted-foreground">{title}</span>
        <span className="text-3xl font-bold">{value}</span>
        {description && (
          <span className="text-xs text-muted-foreground">{description}</span>
        )}
        {trend !== undefined && (
          <div className="flex items-center mt-1">
            <span
              className={`text-xs flex items-center ${
                trend >= 0 ? "text-green-500" : "text-red-500"
              }`}
            >
              {trend >= 0 ? "↑" : "↓"} {Math.abs(trend)}%
            </span>
          </div>
        )}
      </>
    )}
  </div>
);

export default function ReportsPage() {
  const { user: currentUser } = useAuth();
  const router = useRouter();
  const { usersQuery, projectsQuery } = useDashboardData();
  const { data: users = [], isLoading: isLoadingUsers, error: usersError } = usersQuery;
  const { data: projects = [], isLoading: isLoadingProjects, error: projectsError } = projectsQuery;

  const [timeRange, setTimeRange] = useState<TimeRange>("month");
  const [activeTab, setActiveTab] = useState<TabValue>("overview");
  const [isExporting, setIsExporting] = useState(false);

  // Check if user has permission to access reports
  const canAccessReports = currentUser?.role === "admin" || currentUser?.role === "manager";

  // Filter supervisors with useMemo for performance
  const supervisors = useMemo(() =>
    users.filter(user => user.role === "supervisor"),
    [users]
  );

  // Generate analytics data with useMemo to prevent unnecessary recalculations
  const analyticsData = useMemo(() =>
    generateAnalyticsData(projects as Project[], supervisors, timeRange),
    [projects, supervisors, timeRange]
  );

  // Format date for report title
  const getReportPeriod = useCallback(() => {
    const now = new Date();
    const startDate = subMonths(now, timeRange === "month" ? 1 : timeRange === "quarter" ? 3 : 12);
    return `${format(startDate, "MMM d, yyyy")} - ${format(now, "MMM d, yyyy")}`;
  }, [timeRange]);

  // Handle export functionality
  const handleExport = useCallback(() => {
    setIsExporting(true);

    // Simulate export process
    setTimeout(() => {
      try {
        // Create a simple CSV export of project data
        const headers = ["Project ID", "Status", "Last Activity"];
        const csvContent = [
          headers.join(","),
          ...(projects as Project[]).map(project => [
            `"${project.id}"`,
            project.status,
            new Date(project.lastActivity).toLocaleDateString()
          ].join(","))
        ].join("\n");

        // Create download link
        const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.setAttribute("href", url);
        link.setAttribute("download", `project-report-${format(new Date(), "yyyy-MM-dd")}.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        setIsExporting(false);
      } catch (error) {
        console.error("Export failed:", error);
        setIsExporting(false);
      }
    }, 1000);
  }, [projects]);

  // We don't need this since we're checking for errors directly above

  // Loading state
  const isLoading = isLoadingUsers || isLoadingProjects;

  // Show error message if there's an error
  if (usersError || projectsError) {
    return (
      <div className="min-h-screen bg-background flex flex-col">
        <Header />
        <div className="flex-1 container py-6">
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error Loading Data</AlertTitle>
            <AlertDescription>
              {usersError?.toString() || projectsError?.toString() || "Failed to load report data. Please try again."}
            </AlertDescription>
          </Alert>
          <Button onClick={() => router.push("/dashboard")}>
            Return to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  // Access control check
  if (!canAccessReports) {
    return (
      <div className="min-h-screen bg-background flex flex-col">
        <Header />
        <div className="flex-1 flex items-center justify-center">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Access Denied</CardTitle>
              <CardDescription>
                You don&apos;t have permission to access this page.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={() => router.push("/dashboard")}>
                Return to Dashboard
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <Header />
      <div className="flex-1 container py-6 space-y-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Reports</h1>
            <p className="text-muted-foreground">
              Analytics and reports for projects and users
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Select
              value={timeRange}
              onValueChange={(value) => setTimeRange(value as TimeRange)}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select time range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="month">Last Month</SelectItem>
                <SelectItem value="quarter">Last Quarter</SelectItem>
                <SelectItem value="year">Last Year</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={handleExport}
              disabled={isExporting}
            >
              <Download className="h-4 w-4" />
              {isExporting ? "Exporting..." : "Export"}
            </Button>
          </div>
        </div>

        {isLoading ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner />
          </div>
        ) : (
          <Tabs
            value={activeTab}
            onValueChange={(value) => setActiveTab(value as TabValue)}
            className="space-y-6"
          >
            <TabsList>
              <TabsTrigger value="overview" className="flex items-center gap-2">
                <BarChartIcon className="h-4 w-4" />
                Overview
              </TabsTrigger>
              <TabsTrigger value="projects" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Projects
              </TabsTrigger>
              <TabsTrigger value="users" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Users
              </TabsTrigger>
              <TabsTrigger value="timeline" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Timeline
              </TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Performance Overview</CardTitle>
                  <CardDescription>
                    Summary of key metrics for {getReportPeriod()}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                    <StatCard
                      title="Total Projects"
                      value={analyticsData.totalProjects}
                      loading={isLoading}
                    />
                    <StatCard
                      title="Active Projects"
                      value={analyticsData.projectsByStatus.active}
                      description={analyticsData.totalProjects > 0
                        ? `${((analyticsData.projectsByStatus.active / analyticsData.totalProjects) * 100).toFixed(0)}% of total`
                        : 'No projects yet'
                      }
                      loading={isLoading}
                    />
                    <StatCard
                      title="Completed Projects"
                      value={analyticsData.projectsByStatus.completed}
                      description={analyticsData.totalProjects > 0
                        ? `${((analyticsData.projectsByStatus.completed / analyticsData.totalProjects) * 100).toFixed(0)}% of total`
                        : 'No projects yet'
                      }
                      loading={isLoading}
                    />
                    <StatCard
                      title="Avg. Completion Time"
                      value={`${analyticsData.averageCompletionTime.toFixed(1)} days`}
                      loading={isLoading}
                    />
                  </div>

                  <div className="mt-8 h-[300px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={analyticsData.projectTrends}
                        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="period" />
                        <YAxis />
                        <Tooltip content={<CustomTooltip />} />
                        <Legend />
                        <Bar dataKey="newProjects" name="New Projects" fill="#8884d8" />
                        <Bar dataKey="completedProjects" name="Completed Projects" fill="#82ca9d" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Project Status Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[300px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={[
                              { name: "Active", value: analyticsData.projectsByStatus.active },
                              { name: "Completed", value: analyticsData.projectsByStatus.completed },
                              { name: "Archived", value: analyticsData.projectsByStatus.archived },
                              { name: "Suspended", value: analyticsData.projectsByStatus.suspended },
                              { name: "Pending Supervisor", value: analyticsData.projectsByStatus.pending_supervisor },
                            ]}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          >
                            <Cell key="active" fill={STATUS_COLORS.active} />
                            <Cell key="completed" fill={STATUS_COLORS.completed} />
                            <Cell key="archived" fill={STATUS_COLORS.archived} />
                            <Cell key="suspended" fill={STATUS_COLORS.suspended} />
                            <Cell key="pending_supervisor" fill={STATUS_COLORS.pending_supervisor} />
                          </Pie>
                          <Tooltip content={<CustomTooltip />} />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Supervisor Workload</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[300px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={analyticsData.supervisorLoad}
                          layout="vertical"
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis type="number" />
                          <YAxis dataKey="supervisorName" type="category" width={100} />
                          <Tooltip content={<CustomTooltip />} />
                          <Bar dataKey="projectCount" fill="#8884d8" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Projects Tab */}
            <TabsContent value="projects">
              <Card>
                <CardHeader>
                  <CardTitle>Project Metrics</CardTitle>
                  <CardDescription>
                    Detailed project performance metrics
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-center py-12 text-muted-foreground">
                    Detailed project reports will be available in a future update.
                  </p>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Users Tab */}
            <TabsContent value="users">
              <Card>
                <CardHeader>
                  <CardTitle>User Activity</CardTitle>
                  <CardDescription>
                    User engagement and activity metrics
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-center py-12 text-muted-foreground">
                    User activity reports will be available in a future update.
                  </p>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Timeline Tab */}
            <TabsContent value="timeline">
              <Card>
                <CardHeader>
                  <CardTitle>Project Timeline</CardTitle>
                  <CardDescription>
                    Timeline view of project milestones and deadlines
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-center py-12 text-muted-foreground">
                    Timeline reports will be available in a future update.
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}
      </div>
    </div>
  );
}
