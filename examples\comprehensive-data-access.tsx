/**
 * Comprehensive Data Access Examples
 * 
 * This file demonstrates how to retrieve comprehensive data through hooks
 * available in the project. All data access is implemented through hooks
 * rather than direct API calls, following the project's architecture.
 */

import React from 'react';
import { useDashboardData } from '@/hooks/useDashboardData';
import { useAuth } from '@/hooks/useAuth';
import { useProject } from '@/hooks/useProject';
import { useUser } from '@/hooks/useUser';
import { useDocument } from '@/hooks/useDocument';
import { useMilestones } from '@/hooks/useMilestones';
import { useComment } from '@/hooks/useComment';
import { useNotifications } from '@/hooks/useNotifications';
import { useChat } from '@/hooks/useChat';
import { useTeam } from '@/hooks/useTeam';
import { useContacts } from '@/hooks/useContacts';
import { useChatSearch } from '@/hooks/useChatSearch';

/**
 * Example 1: Comprehensive Dashboard Data
 * Uses the enhanced useDashboardData hook to get all dashboard-related data
 */
export function ComprehensiveDashboard() {
  const dashboardData = useDashboardData({
    filters: {
      searchTerm: '',
      projectStatus: 'all',
      userRole: 'all',
      sortBy: 'createdAt',
      sortDirection: 'desc'
    }
  });

  const {
    // Core data
    users,
    projects,
    supervisors,
    usersByRole,
    projectsByStatus,
    
    // Status indicators
    loading,
    error,
    
    // Comprehensive data access methods
    getProjectDocuments,
    getProjectMilestones,
    getDocumentComments,
    getUserNotifications,
    getUserContacts,
    getTeamMembers,
    getAllUserData,
    
    // Actions
    refetch,
    refreshAll,
    invalidateData
  } = dashboardData;

  if (loading) return <div>Loading comprehensive data...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <h2>Comprehensive Dashboard</h2>
      
      {/* Users by Role */}
      <section>
        <h3>Users by Role</h3>
        {Object.entries(usersByRole).map(([role, roleUsers]) => (
          <div key={role}>
            <h4>{role}: {roleUsers.length}</h4>
          </div>
        ))}
      </section>

      {/* Projects by Status */}
      <section>
        <h3>Projects by Status</h3>
        {Object.entries(projectsByStatus).map(([status, statusProjects]) => (
          <div key={status}>
            <h4>{status}: {statusProjects.length}</h4>
          </div>
        ))}
      </section>

      {/* Actions */}
      <section>
        <button onClick={() => refetch()}>Refresh Dashboard</button>
        <button onClick={() => refreshAll()}>Refresh All Data</button>
        <button onClick={() => invalidateData()}>Invalidate Cache</button>
      </section>
    </div>
  );
}

/**
 * Example 2: Project-Specific Data Retrieval
 * Shows how to get all data related to a specific project
 */
export function ProjectDataExample({ projectId }: { projectId: string }) {
  const { useProjectQuery } = useProject();
  const projectQuery = useProjectQuery(projectId);
  
  // Get project-related data using comprehensive hooks
  const dashboardData = useDashboardData();
  const projectDocuments = dashboardData.getProjectDocuments(projectId);
  const projectMilestones = dashboardData.getProjectMilestones(projectId);

  const project = projectQuery.data;

  if (projectQuery.isLoading) return <div>Loading project data...</div>;
  if (!project) return <div>Project not found</div>;

  return (
    <div>
      <h2>Project: {project.title}</h2>
      
      {/* Project Details */}
      <section>
        <p>Status: {project.status}</p>
        <p>Student: {project.studentId}</p>
        <p>Supervisors: {project.supervisorIds?.join(', ')}</p>
      </section>

      {/* Project Documents */}
      <section>
        <h3>Documents ({projectDocuments.data?.length || 0})</h3>
        {projectDocuments.data?.map(doc => (
          <div key={doc.id}>
            <h4>{doc.title}</h4>
            <p>Status: {doc.status}</p>
          </div>
        ))}
      </section>

      {/* Project Milestones */}
      <section>
        <h3>Milestones ({projectMilestones.data?.length || 0})</h3>
        {projectMilestones.data?.map(milestone => (
          <div key={milestone.id}>
            <h4>{milestone.title}</h4>
            <p>Due: {milestone.dueDate}</p>
            <p>Status: {milestone.status}</p>
          </div>
        ))}
      </section>
    </div>
  );
}

/**
 * Example 3: User-Specific Data Retrieval
 * Shows how to get all data related to a specific user
 */
export function UserDataExample({ userId }: { userId: string }) {
  const { user } = useAuth();
  const dashboardData = useDashboardData();
  
  // Get comprehensive user data
  const userData = dashboardData.getAllUserData(userId);
  const userNotifications = dashboardData.getUserNotifications(userId);
  const userContacts = dashboardData.getUserContacts(userId);
  const teamMembers = dashboardData.getTeamMembers(userId);

  return (
    <div>
      <h2>User Data: {userData.user?.name}</h2>
      
      {/* User Projects */}
      <section>
        <h3>Projects ({userData.projects.length})</h3>
        {userData.projects.map(project => (
          <div key={project.id}>
            <h4>{project.title}</h4>
            <p>Status: {project.status}</p>
          </div>
        ))}
      </section>

      {/* User Documents */}
      <section>
        <h3>Documents ({userData.documents.length})</h3>
        {userData.documents.map(doc => (
          <div key={doc.id}>
            <h4>{doc.title}</h4>
            <p>Status: {doc.status}</p>
          </div>
        ))}
      </section>

      {/* User Notifications */}
      <section>
        <h3>Notifications ({userData.notifications.length})</h3>
        {userData.notifications.map(notification => (
          <div key={notification.id}>
            <p>{notification.message}</p>
            <small>{notification.createdAt}</small>
          </div>
        ))}
      </section>

      {/* User Contacts */}
      <section>
        <h3>Contacts ({userData.contacts.length})</h3>
        {userData.contacts.map(contact => (
          <div key={contact.id}>
            <p>{contact.name} - {contact.email}</p>
          </div>
        ))}
      </section>
    </div>
  );
}

/**
 * Example 4: Document-Specific Data Retrieval
 * Shows how to get all data related to a specific document
 */
export function DocumentDataExample({ documentId }: { documentId: string }) {
  const { getById } = useDocument();
  const dashboardData = useDashboardData();
  
  const documentQuery = getById(documentId);
  const documentComments = dashboardData.getDocumentComments(documentId);

  const document = documentQuery.data;

  if (documentQuery.isLoading) return <div>Loading document...</div>;
  if (!document) return <div>Document not found</div>;

  return (
    <div>
      <h2>Document: {document.title}</h2>
      
      {/* Document Details */}
      <section>
        <p>Status: {document.status}</p>
        <p>Project: {document.projectId}</p>
        <p>Student: {document.studentId}</p>
      </section>

      {/* Document Comments */}
      <section>
        <h3>Comments ({documentComments.data?.length || 0})</h3>
        {documentComments.data?.map(comment => (
          <div key={comment.id}>
            <p>{comment.content}</p>
            <small>By: {comment.authorId} at {comment.createdAt}</small>
            {comment.resolved && <span> ✓ Resolved</span>}
          </div>
        ))}
      </section>
    </div>
  );
}

/**
 * Example 5: Chat Data Retrieval
 * Shows how to access chat messages and search functionality
 */
export function ChatDataExample({ recipientId }: { recipientId: string }) {
  const { user } = useAuth();
  const { useChatMessages } = useChat();
  const { searchQuery, setSearchQuery, searchResults, isSearching } = useChatSearch();
  
  const chatMessages = useChatMessages(recipientId, user?.id);

  return (
    <div>
      <h2>Chat with User</h2>
      
      {/* Chat Search */}
      <section>
        <input
          type="text"
          placeholder="Search messages..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
        {isSearching && <p>Searching...</p>}
        {searchResults.length > 0 && (
          <div>
            <h4>Search Results ({searchResults.length})</h4>
            {searchResults.map(message => (
              <div key={message.id}>
                <p>{message.content}</p>
                <small>{message.timestamp.toLocaleString()}</small>
              </div>
            ))}
          </div>
        )}
      </section>

      {/* Chat Messages */}
      <section>
        <h3>Messages</h3>
        {chatMessages.data?.pages.map((page, pageIndex) => (
          <div key={pageIndex}>
            {page.messages.map(message => (
              <div key={message.id}>
                <p>{message.content}</p>
                <small>{message.timestamp.toLocaleString()}</small>
              </div>
            ))}
          </div>
        ))}
      </section>
    </div>
  );
}
