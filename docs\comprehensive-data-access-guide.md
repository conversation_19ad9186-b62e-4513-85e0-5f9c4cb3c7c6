# Comprehensive Data Access Through Hooks

This guide provides a complete overview of all available hooks for data retrieval in the project. All data access is implemented through hooks rather than direct API calls, following the project's architecture principles.

## Core Data Hooks

### 1. `useDashboardData` - Central Data Hub

**Enhanced comprehensive dashboard data hook that aggregates multiple data sources.**

```typescript
import { useDashboardData } from '@/hooks/useDashboardData';

const dashboardData = useDashboardData({
  filters: {
    searchTerm: 'project name',
    projectStatus: 'active',
    userRole: 'student',
    sortBy: 'createdAt',
    sortDirection: 'desc'
  }
});

// Access data
const { 
  users, projects, supervisors,
  usersByRole, projectsByStatus,
  loading, error,
  refetch, refreshAll, invalidateData
} = dashboardData;

// Comprehensive data access methods
const projectDocs = dashboardData.getProjectDocuments(projectId);
const projectMilestones = dashboardData.getProjectMilestones(projectId);
const docComments = dashboardData.getDocumentComments(documentId);
const userNotifications = dashboardData.getUserNotifications(userId);
const userContacts = dashboardData.getUserContacts(userId);
const teamMembers = dashboardData.getTeamMembers(userId);
const allUserData = dashboardData.getAllUserData(userId);
```

### 2. `useAuth` - Authentication & Current User

```typescript
import { useAuth } from '@/hooks/useAuth';

const { 
  user, isLoading, error,
  login, logout, signup,
  requestReset, resetPassword, updatePassword,
  verifySession, requireAuth
} = useAuth();
```

### 3. `useUser` - User Management

```typescript
import { useUser } from '@/hooks/useUser';

const { 
  useUsersQuery,           // Get all users
  useUserQuery,            // Get user by ID
  useUsersByIdsQuery,      // Get multiple users by IDs
  useUserStatusQuery,      // Get user status
  useContactsQuery,        // Get user contacts
  createUser,              // Create new user
  updateUser,              // Update user
  deleteUser               // Delete user
} = useUser();

// Usage examples
const allUsers = useUsersQuery();
const specificUser = useUserQuery(userId);
const userContacts = useContactsQuery(userId);
```

### 4. `useProject` - Project Management

```typescript
import { useProject } from '@/hooks/useProject';

const { 
  useProjectsQuery,                    // Get all projects (with associated data)
  useProjectQuery,                     // Get project by ID
  useProjectWithAssociatedData,        // Get project with milestones & documents
  createProject,                       // Create new project
  updateProject,                       // Update project
  deleteProject,                       // Delete project
  prefetchProjectWithAssociatedData    // Prefetch project data
} = useProject();

// Usage examples
const allProjects = useProjectsQuery();
const filteredProjects = useProjectsQuery({ status: 'active', studentId: userId });
const projectWithData = useProjectWithAssociatedData(projectId);
```

### 5. `useDocument` - Document Management

```typescript
import { useDocument, useDocumentQueries } from '@/hooks/useDocument';

const { 
  getById,        // Get document by ID
  getList,        // Get documents with filters
  create,         // Create document
  update,         // Update document
  remove          // Delete document
} = useDocument();

const { 
  useProjectDocuments,     // Get documents for a project
  useStudentDocuments,     // Get documents for a student
  useSupervisorDocuments   // Get documents for a supervisor
} = useDocumentQueries();

// Usage examples
const document = getById(documentId);
const projectDocs = useProjectDocuments(projectId);
const studentDocs = useStudentDocuments(studentId);
```

### 6. `useMilestones` - Project Milestones

```typescript
import { useMilestones } from '@/hooks/useMilestones';

const milestonesQuery = useMilestones(projectId);
const { data: milestones, isLoading, error } = milestonesQuery;
```

### 7. `useComment` - Document Comments

```typescript
import { useComment, useCommentQueries } from '@/hooks/useComment';

const { 
  create,                  // Create comment
  update,                  // Update comment
  remove,                  // Delete comment
  getById,                 // Get comment by ID
  getDocumentComments      // Get all comments for a document
} = useComment();

const { 
  useResolvedComments,     // Get resolved comments
  useUnresolvedComments,   // Get unresolved comments
  useHighlightComments     // Get highlight comments
} = useCommentQueries();

// Usage examples
const docComments = getDocumentComments(documentId);
const unresolvedComments = useUnresolvedComments(documentId);
```

### 8. `useNotifications` - User Notifications

```typescript
import { useNotifications } from '@/hooks/useNotifications';

const { 
  notifications,           // Array of notifications
  unreadCount,            // Number of unread notifications
  isLoading,              // Loading state
  error,                  // Error state
  markAsRead,             // Mark notification as read
  markAllAsRead,          // Mark all as read
  deleteNotification,     // Delete single notification
  deleteAllNotifications, // Delete all notifications
  createNotification,     // Create new notification
  refetch                 // Refresh notifications
} = useNotifications(userId);
```

### 9. `useChat` - Chat Messages

```typescript
import { useChat } from '@/hooks/useChat';

const { 
  useChatMessages,        // Get chat messages (infinite query)
  sendMessage,            // Send new message
  deleteMessage,          // Delete message
  uploadFile,             // Upload file
  addOptimisticMessage,   // Add optimistic message
  createChatId            // Create chat ID from user IDs
} = useChat();

// Usage examples
const chatMessages = useChatMessages(recipientId, userId);
```

### 10. `useTeam` - Team Management

```typescript
import { useTeam } from '@/hooks/useTeam';

const { 
  useTeamMembersQuery,    // Get team members for a user
  useTeamsQuery           // Get teams for a user
} = useTeam();

// Usage examples
const teamMembers = useTeamMembersQuery(userId);
const userTeams = useTeamsQuery(userId);
```

## Utility Hooks

### 11. `useContacts` - User Contacts

```typescript
import { useContacts } from '@/hooks/useContacts';

const contactsQuery = useContacts(userId);
const { data: contacts, isLoading, error } = contactsQuery;
```

### 12. `useChatSearch` - Chat Message Search

```typescript
import { useChatSearch } from '@/hooks/useChatSearch';

const { 
  searchQuery,            // Current search query
  setSearchQuery,         // Set search query
  searchResults,          // Search results
  isSearching,            // Search loading state
  error,                  // Search error
  searchMessages          // Manual search function
} = useChatSearch();
```

### 13. `useFilters` - Generic Filtering

```typescript
import { useFilters } from '@/hooks/useFilters';

const { 
  filters,                // Current filters
  setFilters,             // Update filters
  resetFilters            // Reset to initial state
} = useFilters(initialFilters);
```

## Best Practices

### 1. Always Use Hooks for Data Access
```typescript
// ✅ Good - Using hooks
const { data: users } = useUsersQuery();

// ❌ Bad - Direct API calls
const users = await getUsers();
```

### 2. Leverage Associated Data
```typescript
// ✅ Good - Projects include milestones and documents
const projects = useProjectsQuery(); // Includes associated data

// ❌ Bad - Separate calls
const projects = useProjectsQuery();
const milestones = useMilestones(projectId);
```

### 3. Use Comprehensive Dashboard Hook
```typescript
// ✅ Good - Single hook for dashboard data
const dashboardData = useDashboardData();
const { users, projects, getProjectDocuments } = dashboardData;

// ❌ Bad - Multiple separate hooks
const users = useUsersQuery();
const projects = useProjectsQuery();
const documents = useDocument();
```

### 4. Handle Loading and Error States
```typescript
const { data, isLoading, error } = useProjectsQuery();

if (isLoading) return <LoadingSpinner />;
if (error) return <ErrorMessage error={error} />;
if (!data) return <NoDataMessage />;

return <ProjectsList projects={data} />;
```

### 5. Use Filtering and Sorting
```typescript
const dashboardData = useDashboardData({
  filters: {
    searchTerm: searchInput,
    projectStatus: selectedStatus,
    sortBy: 'createdAt',
    sortDirection: 'desc'
  }
});
```

## Query Keys Reference

All hooks expose query keys for advanced cache management:

```typescript
const dashboardData = useDashboardData();
const { queryKeys } = dashboardData;

// Available query keys:
// - queryKeys.users
// - queryKeys.projects  
// - queryKeys.milestones
// - queryKeys.documents
// - queryKeys.notifications
// - queryKeys.comments
// - queryKeys.teams

// Manual cache invalidation
queryClient.invalidateQueries({ queryKey: queryKeys.projects });
```

## Real-time Updates

Some hooks support real-time updates:

```typescript
// Chat with real-time updates
import { useRealtimeChat } from '@/hooks/useRealtimeChat';

const { connectionStatus } = useRealtimeChat({
  userId,
  recipientId,
  enabled: true,
  onMessageReceived: (message) => {
    // Handle new message
  },
  onMessageDeleted: (messageId) => {
    // Handle deleted message
  }
});
```

This comprehensive hook system ensures consistent, efficient, and maintainable data access throughout the application.
